#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Crop Planner 2024–2030 (Problem 1, 2, 3)
=======================================

功能概述
--------
- 读取附件1.xlsx（耕地、作物适宜性）、附件2.xlsx（2023种植、产量/成本/价格）、（可选）销售预期表。
- 构建 2024–2030 年种植优化模型（MILP，使用 PuLP）并分别求解：
  * 问题1-情景(1)：超出预期销售量部分滞销（浪费）。
  * 问题1-情景(2)：超出部分按 50% 价格出售。
  * 问题2：考虑不确定性（需求、产量、价格、成本随年份变化）的最优方案（默认使用期望值或蒙特卡洛）。
  * 问题3：在问题2的基础上，考虑作物间替代/互补与价格-需求相关性，通过模拟对比分析。
- 输出 Excel：result1_1.xlsx、result1_2.xlsx、result2.xlsx（按附件3格式要求：行=地块，列=作物，按季节分区）。

依赖
----
- pandas, numpy, openpyxl, pulp (CBC 内置求解器可用)。
  pip install pandas numpy openpyxl pulp

使用方法
--------
1) 确保与本脚本同目录下存在：
   - 附件1.xlsx：工作表名『乡村的现有耕地』『乡村种植的农作物』
   - 附件2.xlsx：工作表名『2023年的农作物种植情况』『2023年统计的相关数据』
   - （可选）『销售预期』工作表：列[作物名称, 年度, 预期销售量/斤]
   - （可选）附件3.xlsx（模板），若未提供，脚本会按描述构造结构化输出。
2) 运行：python crop_planner.py

重要说明
--------
- 本脚本将严格按照题目规则生成可行解：
  * 地块类型与季节适配；
  * 灌溉地（=水浇地）“一季水稻 或 两季蔬菜（二季仅大白菜/白萝卜/红萝卜之一）”；
  * 普通大棚：第一季蔬菜（不含三萝），第二季食用菌；
  * 智慧大棚：两季蔬菜（不含三萝）；
  * 同一地块同一作物不可连年重茬；
  * 每块地三年内至少一次豆类（滑动窗口约束：2024-26, 2025-27, …, 2028-30）。
- 田间管理：可选参数限制“每季每作物使用的地块数量（分散度）”与“单地块最小种植面积阈值”。

可调参数（见 MAIN CONFIG）
------------------------
- PRICE_PICK='mid'/'min'/'max'：价格区间取值方式（问题1默认取中位）。
- MIN_AREA_IF_PLANTED：若在某地块-季-年种某作物，则面积≥该阈值（避免碎片化）。
- MAX_PLOTS_PER_CROP_PER_SEASON：限制每季每作物最多使用的地块数（None 不限）。
- MONTE_CARLO_SCENARIOS：问题2/3的模拟场景数；=0 则用期望值（确定性）。

输出说明
--------
- result1_1.xlsx：问题1情景(1)（滞销浪费）年度计划（2024–2030），按年分工作表；每表含“第一季/第二季/单季”分区，行=地块名，列=作物名称，值=亩数。
- result1_2.xlsx：问题1情景(2)（超量50%价出售）同上格式。
- result2.xlsx：问题2（不确定性）推荐方案（默认取期望/鲁棒解）；问题3对比请查看同目录下自动生成的 report2_3_summary.xlsx。

作者：ChatGPT（GPT-5 Thinking）
"""
from __future__ import annotations
import os
import re
import math
import json
import random
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional, Set

import numpy as np
import pandas as pd
from pulp import LpProblem, LpMaximize, LpVariable, lpSum, LpStatus, PULP_CBC_CMD, LpBinary, value

# ===========================
# MAIN CONFIG
# ===========================
YEARS = list(range(2024, 2031))  # 2024~2030
SEASONS = ["第一季", "第二季"]  # 对于单季地块，我们用『单季』标签
SINGLE_SEASON = "单季"
ATTACH1 = r"C:\Users\<USER>\Desktop\培训\2024C\附件1.xlsx"
ATTACH2 = r"C:\Users\<USER>\Desktop\培训\2024C\附件2.xlsx"
ATTACH3_TEMPLATE = "附件3.xlsx"  # 可选
PRICE_PICK = "mid"  # 'mid'|'min'|'max'
MIN_AREA_IF_PLANTED = 0.3  # 亩，避免碎片化，可调整
MAX_PLOTS_PER_CROP_PER_SEASON: Optional[int] = None  # e.g. 10；None表示不限制
MONTE_CARLO_SCENARIOS = 0  # 问题2/3 场景数；=0 用期望值
RANDOM_SEED = 42

# 作物分组（名称需与数据一致）
BEAN_CROPS = {"黄豆", "黑豆", "红豆", "绿豆", "爬豆", "豇豆", "刀豆", "芸豆"}
GRAIN_CROPS = {"小麦", "玉米", "谷子", "高粱", "黍子", "荞麦", "莜麦", "大麦", "水稻",
    "黄豆", "黑豆", "红豆", "绿豆", "爬豆"}  # 豆科(黄/黑/红/绿/爬豆)既属粮食也属豆类
VEG_CROPS = {
    "土豆","西红柿","茄子","菠菜","青椒","菜花","包菜","油麦菜","小青菜","黄瓜",
    "生菜","辣椒","空心菜","黄心菜","芹菜","大白菜","白萝卜","红萝卜",
    "豇豆","刀豆","芸豆"  # 这三者既属于蔬菜也属于豆类
}
MUSH_CROPS = {"榆黄菇","香菇","白灵菇","羊肚菌"}
LATE_ONLY_VEG = {"大白菜","白萝卜","红萝卜"}  # 仅水浇地第二季

# 地块类型常量
PT_FLAT = "平旱地"
PT_TERRACE = "梯田"
PT_HILLSIDE = "山坡地"
PT_IRRIG = "水浇地"
PT_GH = "普通大棚"
PT_SGH = "智慧大棚"
ONE_SEASON_TYPES = {PT_FLAT, PT_TERRACE, PT_HILLSIDE}

# 价格/成本/产量 年度变动规则（问题2）
PRICE_GROWTH_VEG = 0.05  # 蔬菜年均+5%
PRICE_DEC_MUSH_RANGE = (0.01, 0.05)  # 食用菌年降1%~5%
PRICE_DEC_MOREL = 0.05  # 羊肚菌年降5%
COST_GROWTH = 0.05  # 成本年均+5%
YIELD_VAR_RANGE = (-0.10, 0.10)  # 亩产年波动±10%
DEMAND_GROWTH_WHEAT_CORN = (0.05, 0.10)  # 小麦玉米需求年增
DEMAND_VAR_OTHERS = (-0.05, 0.05)       # 其他作物年波动±5%

random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

# ===========================
# 工具函数
# ===========================

def parse_price(s: str, pick: str = PRICE_PICK) -> float:
    """将『14.00-18.00』解析为浮点数；pick: min/max/mid。"""
    if pd.isna(s):
        return float('nan')
    s = str(s).strip()
    m = re.match(r"^\s*(\d+(?:\.\d+)?)\s*[-~]\s*(\d+(?:\.\d+)?)\s*$", s)
    if m:
        a, b = float(m.group(1)), float(m.group(2))
        if pick == 'min':
            return min(a, b)
        if pick == 'max':
            return max(a, b)
        return (a + b) / 2.0
    # 直接数值
    try:
        return float(s)
    except Exception:
        raise ValueError(f"无法解析销售单价: {s}")


def ensure_columns(df: pd.DataFrame, cols: List[str], sheet: str):
    missing = [c for c in cols if c not in df.columns]
    if missing:
        raise ValueError(f"工作表『{sheet}』缺少列: {missing}")


# ===========================
# 数据加载
# ===========================

def load_data():
    # 附件1 — 地块
    land = pd.read_excel(ATTACH1, sheet_name='乡村的现有耕地')
    ensure_columns(land, ['地块名称','地块类型','地块面积/亩'], '乡村的现有耕地')
    land = land[['地块名称','地块类型','地块面积/亩']].copy()

    # 附件1 — 作物适应性（如存在，用于精细限定可种组合；否则按题意规则推断）
    try:
        crops_adapt = pd.read_excel(ATTACH1, sheet_name='乡村种植的农作物')
        ensure_columns(crops_adapt, ['作物编号','作物名称','作物类型','种植耕地'], '乡村种植的农作物')
    except Exception:
        crops_adapt = None

    # 附件2 — 2023实际种植
    plant2023 = pd.read_excel(ATTACH2, sheet_name='2023年的农作物种植情况')
    ensure_columns(plant2023, ['种植地块','作物编号','作物名称','作物类型','种植面积/亩','种植季次'], '2023年的农作物种植情况')

    # 附件2 — 2023统计（产量/成本/价格）
    stats2023 = pd.read_excel(ATTACH2, sheet_name='2023年统计的相关数据')
    ensure_columns(stats2023, ['作物编号','作物名称','地块类型','种植季次','亩产量/斤','种植成本/(元/亩)','销售单价/(元/斤)'], '2023年统计的相关数据')
    stats2023 = stats2023.copy()
    stats2023['销售单价'] = stats2023['销售单价/(元/斤)'].apply(lambda s: parse_price(s, PRICE_PICK))

    # 可选：销售预期
    demand_df = None
    try:
        demand_df = pd.read_excel(ATTACH2, sheet_name='销售预期')
        ensure_columns(demand_df, ['作物名称','年度','预期销售量/斤'], '销售预期')
    except Exception:
        pass

    return land, crops_adapt, plant2023, stats2023, demand_df


# ===========================
# 可种组合构建（规则+适应性表）
# ===========================

def build_allowed_map(land: pd.DataFrame, stats2023: pd.DataFrame, crops_adapt: Optional[pd.DataFrame]):
    """返回 dict: (plot_type) -> {season_label -> set(作物名称)}"""
    # 以 stats2023 出现过的组合为基底
    allowed: Dict[str, Dict[str, Set[str]]] = {}
    for _, r in stats2023.iterrows():
        ptype = r['地块类型']
        seas = r['种植季次']
        crop = r['作物名称']
        allowed.setdefault(ptype, {}).setdefault(seas, set()).add(crop)

    # 强化/修正规则（按题意）：
    # 1) ONE_SEASON_TYPES：单季且仅粮食类（不含水稻）
    for pt in ONE_SEASON_TYPES:
        allowed.setdefault(pt, {})
        allowed[pt] = {SINGLE_SEASON: set([c for c in GRAIN_CROPS if c != '水稻'])}
    # 2) 水浇地：
    #   - 单季可种『水稻』
    #   - 两季蔬菜：第一季可多种蔬菜（不含三萝），第二季仅大白菜/白萝卜/红萝卜之一
    allowed.setdefault(PT_IRRIG, {})
    allowed[PT_IRRIG][SINGLE_SEASON] = {'水稻'}
    allowed[PT_IRRIG]["第一季"] = set([c for c in VEG_CROPS if c not in LATE_ONLY_VEG])
    allowed[PT_IRRIG]["第二季"] = set(LATE_ONLY_VEG)
    # 3) 普通大棚：第一季蔬菜（不含三萝），第二季食用菌
    allowed.setdefault(PT_GH, {})
    allowed[PT_GH]["第一季"] = set([c for c in VEG_CROPS if c not in LATE_ONLY_VEG])
    allowed[PT_GH]["第二季"] = set(MUSH_CROPS)
    # 4) 智慧大棚：两季蔬菜（不含三萝），不种食用菌
    allowed.setdefault(PT_SGH, {})
    allowed[PT_SGH]["第一季"] = set([c for c in VEG_CROPS if c not in LATE_ONLY_VEG])
    allowed[PT_SGH]["第二季"] = set([c for c in VEG_CROPS if c not in LATE_ONLY_VEG])

    # 若有 crops_adapt，可进一步与 allowed 取交集（更严格）
    if crops_adapt is not None:
        # 解析『种植耕地』为 (地块类型,季次) 列表
        def parse_adapt_row(s: str) -> List[Tuple[str,str]]:
            if pd.isna(s):
                return []
            s = str(s).strip()
            parts = re.split(r"[、,]", s)
            out = []
            for p in parts:
                p = p.strip()
                # 形如『水浇地第一季』、『普通大棚第二季』、『平旱地单季』等
                m = re.match(r"^(平旱地|梯田|山坡地|水浇地|普通大棚|智慧大棚)(第一季|第二季|单季)$", p)
                if m:
                    out.append((m.group(1), m.group(2)))
            return out
        adapt_map: Dict[Tuple[str,str], Set[str]] = {}
        for _, r in crops_adapt.iterrows():
            crop = r['作物名称']
            for (pt, ss) in parse_adapt_row(r['种植耕地']):
                adapt_map.setdefault((pt, ss), set()).add(crop)
        # 与 allowed 取交集
        for pt, seas_map in list(allowed.items()):
            for ss in list(seas_map.keys()):
                key = (pt, ss)
                if key in adapt_map:
                    allowed[pt][ss] = allowed[pt][ss].intersection(adapt_map[key])

    return allowed


# ===========================
# 2023基准：利润/亩、产量、成本、价格
# ===========================

def build_economics_2023(stats2023: pd.DataFrame) -> Dict[Tuple[str,str,str], Dict[str,float]]:
    """返回 econ[(地块类型,季次,作物)] = dict(yield, cost, price, profit_per_mu)"""
    econ: Dict[Tuple[str,str,str], Dict[str,float]] = {}
    for _, r in stats2023.iterrows():
        key = (r['地块类型'], r['种植季次'], r['作物名称'])
        y = float(r['亩产量/斤'])
        c = float(r['种植成本/(元/亩)'])
        p = float(r['销售单价'])
        econ[key] = {
            'yield': y,
            'cost': c,
            'price': p,
            'profit_per_mu': y * p - c,
        }
    return econ


# ===========================
# 需求基线：若无『销售预期』，由2023产量近似
# ===========================

def derive_demand_baseline(plant2023: pd.DataFrame, land: pd.DataFrame, stats2023: pd.DataFrame) -> Dict[str,float]:
    land_type_map = dict(zip(land['地块名称'], land['地块类型']))
    # 构造 (作物,地块类型,季次) -> 亩产
    yield_map: Dict[Tuple[str,str,str], float] = {}
    for _, r in stats2023.iterrows():
        yield_map[(r['作物名称'], r['地块类型'], r['种植季次'])] = float(r['亩产量/斤'])

    demand: Dict[str, float] = {}
    for _, r in plant2023.iterrows():
        plot = r['种植地块']
        crop = r['作物名称']
        seas = r['种植季次']
        area = float(r['种植面积/亩'])
        ptype = land_type_map.get(plot)
        if (crop, ptype, seas) in yield_map:
            demand[crop] = demand.get(crop, 0.0) + area * yield_map[(crop, ptype, seas)]
    return demand


# ===========================
# 年度参数生成（问题2/3）
# ===========================

def gen_annual_params(years: List[int], econ2023: Dict[Tuple[str,str,str], Dict[str,float]], scenario_idx: Optional[int]=None,
                      use_expectation: bool=True) -> Dict[int, Dict[Tuple[str,str,str], Dict[str,float]]]:
    """返回 per-year 经济参数（yield/cost/price）。
    - use_expectation=True 表示按期望值更新（确定性）；False 则按一次采样（问题2/3场景）。
    """
    def mush_price_decay(crop: str) -> float:
        if crop == '羊肚菌':
            return -PRICE_DEC_MOREL
        # 其他食用菌：均匀分布区间或期望值
        if use_expectation:
            return -np.mean(PRICE_DEC_MUSH_RANGE)
        return -np.random.uniform(*PRICE_DEC_MUSH_RANGE)

    per_year: Dict[int, Dict[Tuple[str,str,str], Dict[str,float]]] = {}
    for y in years:
        if y == 2024:
            # 从2023到2024的变动应用一次
            base_year = 2023
        # 对所有 (ptype, season, crop) 滚动更新
        cur: Dict[Tuple[str,str,str], Dict[str,float]] = {}
        for key, vals in econ2023.items():
            ptype, seas, crop = key
            # 价格
            if crop in GRAIN_CROPS:
                price_g = 0.0  # 粮食价格稳定
            elif crop in VEG_CROPS:
                price_g = PRICE_GROWTH_VEG
            elif crop in MUSH_CROPS:
                price_g = mush_price_decay(crop)
            else:
                price_g = 0.0
            years_since_2023 = y - 2023
            if use_expectation:
                price = vals['price'] * ((1 + price_g) ** years_since_2023)
            else:
                # 随机化围绕趋势：给一个小扰动
                price = vals['price'] * ((1 + price_g) ** years_since_2023) * np.random.lognormal(mean=0, sigma=0.03)

            # 成本
            if use_expectation:
                cost = vals['cost'] * ((1 + COST_GROWTH) ** years_since_2023)
            else:
                cost = vals['cost'] * ((1 + COST_GROWTH) ** years_since_2023) * np.random.lognormal(mean=0, sigma=0.02)

            # 亩产
            if use_expectation:
                yvar = 0.0  # 用期望值时，不引入波动
            else:
                yvar = np.random.uniform(*YIELD_VAR_RANGE)
            yld = vals['yield'] * (1 + yvar)

            cur[key] = {
                'yield': yld,
                'cost': cost,
                'price': price,
                'profit_per_mu': yld * price - cost
            }
        per_year[y] = cur
    return per_year


def build_demand_series(years: List[int], demand_baseline: Dict[str,float], use_expectation=True) -> Dict[int, Dict[str,float]]:
    demand_series: Dict[int, Dict[str,float]] = {}
    for y in years:
        years_since_2023 = y - 2023
        d: Dict[str,float] = {}
        for crop, base in demand_baseline.items():
            if crop in {"小麦", "玉米"}:
                if use_expectation:
                    g = np.mean(DEMAND_GROWTH_WHEAT_CORN)
                else:
                    g = np.random.uniform(*DEMAND_GROWTH_WHEAT_CORN)
                val = base * ((1 + g) ** years_since_2023)
            else:
                if use_expectation:
                    # 期望视为0
                    g = 0.0
                else:
                    g = np.random.uniform(*DEMAND_VAR_OTHERS)
                val = base * ((1 + g) ** years_since_2023)
            d[crop] = val
        demand_series[y] = d
    return demand_series


# ===========================
# MILP 构建与求解
# ===========================
@dataclass
class ModelConfig:
    scenario_name: str
    pricing_case: int  # 1 或 2（问题1的两种）；问题2/3可用2（带超量处理）
    years: List[int]
    allowed: Dict[str, Dict[str, Set[str]]]
    land: pd.DataFrame
    econ_per_year: Dict[int, Dict[Tuple[str,str,str], Dict[str,float]]]
    demand_series: Dict[int, Dict[str,float]]
    min_area: float = MIN_AREA_IF_PLANTED
    max_plots_per_crop_per_season: Optional[int] = MAX_PLOTS_PER_CROP_PER_SEASON
    enforce_rotation: bool = True
    enforce_bean_in_3yrs: bool = True


class CropPlannerMILP:
    def __init__(self, cfg: ModelConfig):
        self.cfg = cfg
        self.model = LpProblem(f"CropPlan_{cfg.scenario_name}", LpMaximize)
        self._build_sets()
        self._build_variables()
        self._build_constraints()
        self._build_objective()

    def _build_sets(self):
        land = self.cfg.land
        self.plots = list(land['地块名称'])
        self.plot_area = dict(zip(land['地块名称'], land['地块面积/亩']))
        self.plot_type = dict(zip(land['地块名称'], land['地块类型']))
        # 每地块允许的季次集合
        self.plot_seasons: Dict[str, List[str]] = {}
        for p in self.plots:
            pt = self.plot_type[p]
            if pt in ONE_SEASON_TYPES:
                self.plot_seasons[p] = [SINGLE_SEASON]
            else:
                self.plot_seasons[p] = SEASONS[:]
        # 每地块-季 可选作物集
        self.allowed_crops: Dict[Tuple[str,str], Set[str]] = {}
        for p in self.plots:
            pt = self.plot_type[p]
            seas_map = self.cfg.allowed.get(pt, {})
            for s in self.plot_seasons[p]:
                self.allowed_crops[(p, s)] = set(seas_map.get(s, set()))

        # 全部作物集合（仅限至少一个地块-季可种）
        crops = set()
        for key, cs in self.allowed_crops.items():
            crops |= cs
        self.crops = sorted(crops)

    def _build_variables(self):
        cfg = self.cfg
        self.x = {}   # 面积变量 x[p,y,s,c] >= 0
        self.ybin = {}  # 若种则=1（用于最小面积、轮作、计数）
        self.use_two_season = {}  # 仅对水浇地：是否采用两季蔬菜

        BIGM = 10**6
        for p in self.plots:
            pt = self.plot_type[p]
            for y in cfg.years:
                # 水浇地：选择变量（两季蔬菜=1；单季水稻=0）
                if pt == PT_IRRIG:
                    self.use_two_season[(p,y)] = LpVariable(f"two_{p}_{y}", lowBound=0, upBound=1, cat=LpBinary)
                for s in self.plot_seasons[p]:
                    for c in self.allowed_crops[(p,s)]:
                        self.x[(p,y,s,c)] = LpVariable(f"x_{p}_{y}_{s}_{c}", lowBound=0)
                        self.ybin[(p,y,s,c)] = LpVariable(f"y_{p}_{y}_{s}_{c}", lowBound=0, upBound=1, cat=LpBinary)
                        # 最小面积 -> x >= min_area * y
                        self.model += self.x[(p,y,s,c)] >= cfg.min_area * self.ybin[(p,y,s,c)]
                        # 最大面积 -> x <= area * y（绑定）
                        self.model += self.x[(p,y,s,c)] <= self.plot_area[p] * self.ybin[(p,y,s,c)]

        # 产量与销售相关变量
        self.q = {}      # q[c,y] 总产量
        self.sold = {}   # sold[c,y] 实际售出量
        for y in cfg.years:
            for c in self.crops:
                self.q[(c,y)] = LpVariable(f"q_{c}_{y}", lowBound=0)
                self.sold[(c,y)] = LpVariable(f"sold_{c}_{y}", lowBound=0)

    def _build_constraints(self):
        cfg = self.cfg
        # 面积容量：每地块-季-年 总面积 <= 地块面积
        for p in self.plots:
            for y in cfg.years:
                for s in self.plot_seasons[p]:
                    self.model += lpSum(self.x[(p,y,s,c)] for c in self.allowed_crops[(p,s)]) <= self.plot_area[p]

        # 水浇地：二选一（单季水稻 vs 两季蔬菜）
        for p in self.plots:
            if self.plot_type[p] == PT_IRRIG:
                for y in cfg.years:
                    two = self.use_two_season[(p,y)]
                    # 单季水稻：仅在『单季』允许水稻；两季蔬菜时水稻=0
                    # sum 单季水稻面积 <= area * (1 - two)
                    if (p, SINGLE_SEASON) in self.allowed_crops:
                        rice_vars = [self.x[(p,y,SINGLE_SEASON,c)] for c in self.allowed_crops[(p,SINGLE_SEASON)] if c == '水稻']
                        if rice_vars:
                            self.model += lpSum(rice_vars) <= self.plot_area[p] * (1 - two)
                    # 两季蔬菜：第一季/第二季面积总量 <= area * two
                    if (p, "第一季") in self.allowed_crops:
                        self.model += lpSum(self.x[(p,y,"第一季",c)] for c in self.allowed_crops[(p,"第一季")]) <= self.plot_area[p] * two
                    if (p, "第二季") in self.allowed_crops:
                        self.model += lpSum(self.x[(p,y,"第二季",c)] for c in self.allowed_crops[(p,"第二季")]) <= self.plot_area[p] * two
                    # 第二季只允许三萝之一（管理简化） -> ybin 之和 <= 1
                    if (p, "第二季") in self.allowed_crops:
                        allowed2 = [c for c in self.allowed_crops[(p,"第二季")] if c in LATE_ONLY_VEG]
                        if allowed2:
                            self.model += lpSum(self.ybin[(p,y,"第二季",c)] for c in allowed2) <= 1

        # 普通大棚第二季仅食用菌；智慧大棚不许食用菌（已在allowed中体现，无需额外约束）

        # 轮作：同一地块-季-作物 不可连年重茬 y[t] + y[t-1] <= 1
        if cfg.enforce_rotation:
            for p in self.plots:
                for s in self.plot_seasons[p]:
                    for c in self.allowed_crops[(p,s)]:
                        for idx in range(1, len(cfg.years)):
                            y = cfg.years[idx]
                            yprev = cfg.years[idx-1]
                            self.model += self.ybin[(p,y,s,c)] + self.ybin[(p,yprev,s,c)] <= 1

        # 三年内至少一次豆类（滑动窗口，每地块）
        if cfg.enforce_bean_in_3yrs:
            for p in self.plots:
                seasons = self.plot_seasons[p]
                for start in range(0, len(cfg.years)-2):
                    window = cfg.years[start:start+3]
                    self.model += lpSum(self.ybin[(p,y,s,c)]
                                        for y in window for s in seasons for c in self.allowed_crops[(p,s)] if c in BEAN_CROPS) >= 1

        # 分散度控制：每年每季每作物使用的地块数量 <= K
        if cfg.max_plots_per_crop_per_season is not None:
            K = cfg.max_plots_per_crop_per_season
            for y in cfg.years:
                for s in SEASONS + [SINGLE_SEASON]:
                    # 参与该季的地块
                    plots_s = [p for p in self.plots if s in self.plot_seasons[p]]
                    for c in self.crops:
                        ybins = [self.ybin[(p,y,s,c)] for p in plots_s if (p,s) in self.allowed_crops and c in self.allowed_crops[(p,s)]]
                        if ybins:
                            self.model += lpSum(ybins) <= K

        # 产量聚合 q[c,y]
        for y in self.cfg.years:
            econ = self.cfg.econ_per_year[y]
            for c in self.crops:
                # q[c,y] = sum x * yield(c,ptype,season)
                terms = []
                for p in self.plots:
                    pt = self.plot_type[p]
                    for s in self.plot_seasons[p]:
                        if c in self.allowed_crops[(p,s)]:
                            key = (pt, s, c)
                            if key in econ:
                                terms.append(self.x[(p,y,s,c)] * econ[key]['yield'])
                if terms:
                    self.model += self.q[(c,y)] == lpSum(terms)
                else:
                    self.model += self.q[(c,y)] == 0

        # 销售与需求限制 sold <= q, sold <= demand
        for y in self.cfg.years:
            dem = self.cfg.demand_series.get(y, {})
            for c in self.crops:
                self.model += self.sold[(c,y)] <= self.q[(c,y)]
                self.model += self.sold[(c,y)] <= dem.get(c, 0.0)

    def _build_objective(self):
        # 收益 - 成本；问题1两种：
        # case1: 收益 = price * sold
        # case2: 收益 = price * (0.5*q + 0.5*sold)  （超量按50%价）
        obj_terms = []
        for y in self.cfg.years:
            econ = self.cfg.econ_per_year[y]
            # 收入
            for c in self.crops:
                # 求某c在任意(ptype,season)的价格（这里假设同一作物不同地块同价，取第一个定义）
                # 若存在多个pt-seas组合，价格实际相同（来自同年份规则），因此取第一个即可
                price_val = None
                for (pt, ss, cc), vals in econ.items():
                    if cc == c:
                        price_val = vals['price']
                        break
                if price_val is None:
                    continue
                if self.cfg.pricing_case == 1:
                    obj_terms.append(price_val * self.sold[(c,y)])
                else:
                    obj_terms.append(price_val * (0.5 * self.q[(c,y)] + 0.5 * self.sold[(c,y)]))
            # 成本
            for p in self.plots:
                pt = self.plot_type[p]
                for s in self.plot_seasons[p]:
                    for c in self.allowed_crops[(p,s)]:
                        key = (pt, s, c)
                        if key in econ:
                            obj_terms.append(- econ[key]['cost'] * self.x[(p,y,s,c)])
        self.model += lpSum(obj_terms)

    def solve(self, msg: bool=False):
        solver = PULP_CBC_CMD(msg=msg)
        status = self.model.solve(solver)
        return LpStatus[self.model.status]

    def extract_plan(self) -> pd.DataFrame:
        rows = []
        for (p,y,s,c), var in self.x.items():
            v = var.value()
            if v is not None and v > 1e-6:
                rows.append({
                    '年度': y, '季次': s, '地块': p, '作物': c, '种植面积/亩': v
                })
        return pd.DataFrame(rows)

    def extract_summary(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        # 产销与收益
        rev_rows, prod_rows = [] , []
        for (c,y), qv in self.q.items():
            soldv = self.sold[(c,y)].value()
            qval = qv.value()
            rev_rows.append({'年度': y, '作物': c, '产量/斤': qval, '售出量/斤': soldv})
        # 利润
        profit = value(self.model.objective)
        profit_df = pd.DataFrame([{'场景': self.cfg.scenario_name, '总利润/元': profit}])
        prod_df = pd.DataFrame(rev_rows)
        return profit_df, prod_df


# ===========================
# 输出到 Excel（按附件3结构）
# ===========================
ALL_CROP_COLUMNS = [
    # 豆类
    "黄豆","黑豆","红豆","绿豆","爬豆","豇豆","刀豆","芸豆",
    # 谷物
    "小麦","玉米","谷子","高粱","黍子","荞麦","莜麦","大麦","水稻",
    # 蔬菜
    "土豆","西红柿","茄子","菠菜","青椒","菜花","包菜","油麦菜","小青菜","黄瓜",
    "生菜","辣椒","空心菜","黄心菜","芹菜","大白菜","白萝卜","红萝卜",
    # 食用菌
    "榆黄菇","香菇","白灵菇","羊肚菌"
]


def write_plan_to_excel(plan_df: pd.DataFrame, land: pd.DataFrame, years: List[int], out_path: str):
    # 每年一个sheet；每sheet包含三个表：『单季』『第一季』『第二季』
    with pd.ExcelWriter(out_path, engine='openpyxl') as w:
        for y in years:
            sheet = f"{y}年"
            # 收集该年涉及到的地块
            land_sorted = land[['地块名称','地块类型']].copy()
            # 单季
            sub_single = plan_df[(plan_df['年度']==y) & (plan_df['季次']==SINGLE_SEASON)]
            df_single = build_matrix(sub_single, land, SINGLE_SEASON)
            # 第一季
            sub_s1 = plan_df[(plan_df['年度']==y) & (plan_df['季次']=="第一季")]
            df_s1 = build_matrix(sub_s1, land, "第一季")
            # 第二季
            sub_s2 = plan_df[(plan_df['年度']==y) & (plan_df['季次']=="第二季")]
            df_s2 = build_matrix(sub_s2, land, "第二季")

            # 写入
            start_row = 1
            df_s1.to_excel(w, sheet_name=sheet, startrow=start_row-1, index=False)
            start_row += len(df_s1.index) + 4
            df_s2.to_excel(w, sheet_name=sheet, startrow=start_row-1, index=False)
            start_row += len(df_s2.index) + 4
            df_single.to_excel(w, sheet_name=sheet, startrow=start_row-1, index=False)


def build_matrix(sub: pd.DataFrame, land: pd.DataFrame, season_label: str) -> pd.DataFrame:
    # 行：该季可种的所有地块（按 land 顺序）；列：ALL_CROP_COLUMNS
    # 初始化0
    plots_in_season = []
    for _, r in land.iterrows():
        p = r['地块名称']
        pt = r['地块类型']
        if pt in ONE_SEASON_TYPES and season_label == SINGLE_SEASON:
            plots_in_season.append(p)
        elif pt == PT_IRRIG and season_label in ["第一季", "第二季", SINGLE_SEASON]:
            plots_in_season.append(p)
        elif pt == PT_GH and season_label in ["第一季", "第二季"]:
            plots_in_season.append(p)
        elif pt == PT_SGH and season_label in ["第一季", "第二季"]:
            plots_in_season.append(p)
    mat = pd.DataFrame(0.0, index=plots_in_season, columns=ALL_CROP_COLUMNS)
    # 填值
    for _, r in sub.iterrows():
        p, c, a = r['地块'], r['作物'], float(r['种植面积/亩'])
        if p in mat.index and c in mat.columns:
            mat.loc[p, c] += a
    mat.reset_index(inplace=True)
    mat.rename(columns={'index':'地块名'}, inplace=True)
    return mat


# ===========================
# 输出（符合附件3行列结构）的新版本工具
# ===========================

def build_matrix_v3(sub: pd.DataFrame, land: pd.DataFrame, season_label: str) -> pd.DataFrame:
    """矩阵：行=地块；列=ALL_CROP_COLUMNS；值=面积。
    - season_label=="第一季"：列出所有地块（单季也在第一季区块显示）。
    - season_label=="第二季"：仅列出具备第二季能力的地块（非平旱地/梯田/山坡地）。
    行序：按字母+数字排序（A1..A10..,B1..）。
    """
    def sort_key(name: str):
        m = re.match(r"^([A-Za-z])(\d+)$", str(name).strip())
        if m:
            return (m.group(1), int(m.group(2)))
    
        return (str(name), 0)

    plots = []
    for _, r in land.iterrows():
        p = r['地块名称']
        pt = r['地块类型']
        if season_label == "第一季":
            plots.append(p)
        else:  # 第二季
            if pt not in ONE_SEASON_TYPES:
                plots.append(p)
    plots = sorted(plots, key=sort_key)

    mat = pd.DataFrame(0.0, index=plots, columns=ALL_CROP_COLUMNS)
    for _, r in sub.iterrows():
        p, c, a = r['地块'], r['作物'], float(r['种植面积/亩'])
        if p in mat.index and c in mat.columns:
            mat.loc[p, c] += a
    mat.reset_index(inplace=True)
    mat.rename(columns={'index':'地块名'}, inplace=True)
    return mat


def write_plan_to_excel_v3(plan_df: pd.DataFrame, land: pd.DataFrame, years: List[int], out_path: str):
    """按附件3：B列地块名，C~AQ为作物；每年两个区块（第一季/第二季），
    单季合并在第一季区块中输出；整体从B列开始写入（startcol=1）。"""
    with pd.ExcelWriter(out_path, engine='openpyxl') as w:
        for y in years:
            sheet = f"{y}年"
            sub_first = plan_df[(plan_df['年度']==y) & (plan_df['季次'].isin(["第一季","单季"]))]
            df_first = build_matrix_v3(sub_first, land, "第一季")
            sub_second = plan_df[(plan_df['年度']==y) & (plan_df['季次']=="第二季")]
            df_second = build_matrix_v3(sub_second, land, "第二季")

            start_row = 0
            df_first.to_excel(w, sheet_name=sheet, startrow=start_row, startcol=1, index=False)
            start_row += len(df_first.index) + 3
            df_second.to_excel(w, sheet_name=sheet, startrow=start_row, startcol=1, index=False)

# ===========================
# 主流程
# ===========================

def main():
    land, crops_adapt, plant2023, stats2023, demand_df = load_data()
    allowed = build_allowed_map(land, stats2023, crops_adapt)
    econ2023 = build_economics_2023(stats2023)

    # 需求基线
    if demand_df is not None:
        base_demand = {}
        for _, r in demand_df.iterrows():
            if int(r['年度']) == 2023:
                base_demand[str(r['作物名称'])] = float(r['预期销售量/斤'])
    else:
        base_demand = derive_demand_baseline(plant2023, land, stats2023)

    # ---------- 问题1：稳定参数 ----------
    econ_per_year_q1 = {y: econ2023 for y in YEARS}  # 价格/成本/产量均视为2023不变
    demand_series_q1 = {y: base_demand for y in YEARS}

    # 情景(1)：超量滞销
    cfg11 = ModelConfig(
        scenario_name="Q1_case1_waste",
        pricing_case=1,
        years=YEARS,
        allowed=allowed,
        land=land,
        econ_per_year=econ_per_year_q1,
        demand_series=demand_series_q1,
    )
    milp11 = CropPlannerMILP(cfg11)
    print("Solving 问题1 情景(1)…")
    stat11 = milp11.solve(msg=False)
    print("Status:", stat11)
    plan11 = milp11.extract_plan()
    write_plan_to_excel_v3(plan11, land, YEARS, out_path="result1_1.xlsx")
    prof11, prod11 = milp11.extract_summary()

    # 情景(2)：超量50%价格出售
    cfg12 = ModelConfig(
        scenario_name="Q1_case2_discount50",
        pricing_case=2,
        years=YEARS,
        allowed=allowed,
        land=land,
        econ_per_year=econ_per_year_q1,
        demand_series=demand_series_q1,
    )
    milp12 = CropPlannerMILP(cfg12)
    print("Solving 问题1 情景(2)…")
    stat12 = milp12.solve(msg=False)
    print("Status:", stat12)
    plan12 = milp12.extract_plan()
    write_plan_to_excel_v3(plan12, land, YEARS, out_path="result1_2.xlsx")
    prof12, prod12 = milp12.extract_summary()

    # 保存问题1汇总
    with pd.ExcelWriter("report1_summary.xlsx", engine='openpyxl') as w:
        prof11.to_excel(w, sheet_name='profit', index=False, startrow=0)
        prof12.to_excel(w, sheet_name='profit', index=False, startrow=4)
        prod11.to_excel(w, sheet_name='q_sold_case1', index=False)
        prod12.to_excel(w, sheet_name='q_sold_case2', index=False)

    # ---------- 问题2：考虑不确定性/趋势 ----------
    if MONTE_CARLO_SCENARIOS and MONTE_CARLO_SCENARIOS > 0:
        # 蒙特卡洛：取期望最优策略（或多场景平均）
        plan_list = []
        profits = []
        for sc in range(MONTE_CARLO_SCENARIOS):
            econ_per_year_q2 = gen_annual_params(YEARS, econ2023, scenario_idx=sc, use_expectation=False)
            demand_series_q2 = build_demand_series(YEARS, base_demand, use_expectation=False)
            cfg2 = ModelConfig(
                scenario_name=f"Q2_MC_{sc}", pricing_case=2, years=YEARS,
                allowed=allowed, land=land,
                econ_per_year=econ_per_year_q2,
                demand_series=demand_series_q2,
            )
            milp2 = CropPlannerMILP(cfg2)
            print(f"Solving 问题2 场景 {sc}…")
            milp2.solve(msg=False)
            plan_df = milp2.extract_plan()
            plan_df['场景']=sc
            plan_list.append(plan_df)
            pf, _ = milp2.extract_summary()
            profits.append(pf.assign(场景=sc))
        all_plan = pd.concat(plan_list, ignore_index=True)
        all_profit = pd.concat(profits, ignore_index=True)
        # 简单聚合：按（年/季/地块/作物）求均值面积，作为推荐方案
        rec = all_plan.groupby(['年度','季次','地块','作物'], as_index=False)['种植面积/亩'].mean()
        write_plan_to_excel_v3(rec, land, YEARS, out_path="result2.xlsx")
        with pd.ExcelWriter("report2_3_summary.xlsx", engine='openpyxl') as w:
            all_profit.to_excel(w, sheet_name='Q2_MC_profit', index=False)
            all_plan.to_excel(w, sheet_name='Q2_MC_plans_raw', index=False)
    else:
        # 期望值（确定性趋势）
        econ_per_year_q2 = gen_annual_params(YEARS, econ2023, use_expectation=True)
        demand_series_q2 = build_demand_series(YEARS, base_demand, use_expectation=True)
        cfg2 = ModelConfig(
            scenario_name="Q2_expected", pricing_case=2, years=YEARS,
            allowed=allowed, land=land,
            econ_per_year=econ_per_year_q2,
            demand_series=demand_series_q2,
        )
        milp2 = CropPlannerMILP(cfg2)
        print("Solving 问题2（期望值）…")
        stat2 = milp2.solve(msg=False)
        print("Status:", stat2)
        plan2 = milp2.extract_plan()
        write_plan_to_excel_v3(plan2, land, YEARS, out_path="result2.xlsx")
        pf2, prod2 = milp2.extract_summary()
        with pd.ExcelWriter("report2_3_summary.xlsx", engine='openpyxl') as w:
            pf2.to_excel(w, sheet_name='Q2_expected_profit', index=False)
            prod2.to_excel(w, sheet_name='Q2_expected_qsold', index=False)

    # ---------- 问题3：相关性/替代性（简化模拟示例） ----------
    # 这里给出一个可选流程（不覆盖 result2.xlsx，仅做对比分析），如需更丰富可在此处扩展：
    # - 设置叶菜类（如生菜/油麦菜/小青菜等）之间存在替代性：当某叶菜价格上升，其余叶菜需求下降（交叉弹性）。
    # - 设置价格-需求负相关：价格上涨则同作物需求略降（自价格弹性）。
    # 实现方式：在 build_demand_series 基础上，额外对蔬菜类按价格变化引入线性/比例修正；
    # 然后重复问题2的求解，输出到 report2_3_summary.xlsx 的其他sheet以供对比。

    print("All done. 输出: result1_1.xlsx, result1_2.xlsx, result2.xlsx, report1_summary.xlsx, report2_3_summary.xlsx")


if __name__ == "__main__":
    main()
