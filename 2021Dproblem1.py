# 问题1：计算每根尾坯的最优切割方案（目标9.5米，范围9.0~10.0米）
tail_lengths = [109.0, 93.4, 80.9, 72.0, 62.7, 52.5, 44.9, 42.7, 31.6, 22.7, 14.5, 13.7]

# 报废损失计算函数：结合目标范围[9.0,10.0]与基本要求
def scrap_length(l):
    # l: 单段长度（米）
    # 若小于8.0米，整个段报废；若超出目标范围，超出部分报废
    if l < 8.0:
        return l
    if l < 9.0:
        return 9.0 - l
    if l <= 10.0:
        return 0.0
    if l <= 12.6:
        return l - 10.0
    # 不允许 >12.6
    return float('inf')

# 对每根尾坯做动态规划（不考虑工艺时间等因素）
for L in tail_lengths:
    L_dm = int(round(L*10))  # 转为分米整数
    # DP[x] = 切割出总长为 x 分米时的最小报废长度（分米）
    maxL = L_dm
    INF = 10**9
    DP = [INF]*(maxL+1)
    parent = [None]*(maxL+1)  # 记录路径
    DP[0] = 0
    parent[0] = (-1, 0)
    # 可选切割段长度（分米）
    allowed = list(range(48, 127))  # 48dm=4.8m, …, 126dm=12.6m
    # DP 0-1背包（完全背包）
    for x in range(maxL+1):
        if DP[x] == INF: 
            continue
        for l in allowed:
            nxt = x + l
            if nxt > maxL:
                break
            # 计算此段报废长度（分米）
            scrap = 0
            ld = l / 10.0
            if ld < 8.0:
                scrap = l
            elif ld < 9.0:
                scrap = int(round((9.0 - ld) * 10))
            elif ld <= 10.0:
                scrap = 0
            elif ld <= 12.6:
                scrap = int(round((ld - 10.0) * 10))
            else:
                scrap = INF
            if DP[x] + scrap < DP[nxt]:
                DP[nxt] = DP[x] + scrap
                parent[nxt] = (x, l)
    # 找到最优 x，考虑剩余段
    best_scrap = INF
    best_x = None
    best_left = 0
    for x in range(maxL+1):
        if DP[x] == INF: 
            continue
        leftover = maxL - x
        # 剩余<48分米，则全报废；否则按同样规则计算剩余报废
        if leftover > 0:
            if leftover < 48:
                scr = DP[x] + leftover
            else:
                ld = leftover / 10.0
                if ld < 8.0:
                    scr = DP[x] + leftover
                elif ld < 9.0:
                    scr = DP[x] + int(round((9.0 - ld) * 10))
                elif ld <= 10.0:
                    scr = DP[x]
                else:
                    scr = DP[x] + int(round((ld - 10.0) * 10))
        else:
            scr = DP[x]
        if scr < best_scrap:
            best_scrap = scr
            best_x = x
            best_left = leftover
    # 回溯切割方案
    pieces = []
    cur = best_x
    while cur != 0:
        pre, l = parent[cur]
        pieces.append(l)
        cur = pre
    pieces.reverse()
    # 如果剩余段>=4.8m，则将其视为一段
    if best_left >= 48:
        pieces.append(best_left)
    # 输出结果
    pieces_m = [l/10.0 for l in pieces]
    loss_m = best_scrap/10.0
    print(f"尾坯长度 {L:.1f} m: 切割方案 {pieces_m} (m)，切割损失 {loss_m:.1f} m")
# 问题2：模拟异常时刻切割方案（目标9.5米，范围9.0~10.0米）
anomaly_times = [0.0, 45.6, 98.6, 131.5, 190.8, 233.3, 266.0, 270.7, 327.9]
# 计算每段钢坯长度（异常之间的时间差减去0.8）
segments = []
for i in range(len(anomaly_times)-1):
    dt = anomaly_times[i+1] - anomaly_times[i]
    L = round(dt - 0.8, 1)
    if L >= 4.8:
        segments.append((anomaly_times[i], L))
# 对每段计算最优切割方案
initial_schemes = {}
losses = {}
for (t0, L) in segments:
    L_dm = int(round(L*10))
    # 使用前述DP函数计算最佳方案
    DP = [10**9]*(L_dm+1); DP[0] = 0
    parent = [None]*(L_dm+1)
    allowed = list(range(48, 127))
    for x in range(L_dm+1):
        if DP[x] >= 10**9: continue
        for l in allowed:
            nxt = x + l
            if nxt > L_dm: break
            ld = l/10.0
            # 计算分段报废长度（与问题1相同）
            if ld < 8.0: sc = l
            elif ld < 9.0: sc = int(round((9.0-ld)*10))
            elif ld <= 10.0: sc = 0
            else: sc = int(round((ld-10.0)*10))
            if DP[x] + sc < DP[nxt]:
                DP[nxt] = DP[x] + sc
                parent[nxt] = (x, l)
    # 找最小损失组合
    best = (10**9, None, None)
    for x in range(L_dm+1):
        if DP[x] >= 10**9: continue
        rem = L_dm - x
        if rem > 0:
            if rem < 48:
                total_sc = DP[x] + rem
            else:
                ld = rem/10.0
                if ld < 8.0: total_sc = DP[x] + rem
                elif ld < 9.0: total_sc = DP[x] + int(round((9.0-ld)*10))
                elif ld <= 10.0: total_sc = DP[x]
                else: total_sc = DP[x] + int(round((ld-10.0)*10))
        else:
            total_sc = DP[x]
        if total_sc < best[0]:
            best = (total_sc, x, rem)
    scrap = best[0]
    end_x = best[1]
    rem = best[2]
    # 回溯切割方案
    pieces = []
    cur = end_x
    while cur != 0:
        prev, l = parent[cur]
        pieces.append(l)
        cur = prev
    pieces.reverse()
    if rem and rem >= 48: 
        pieces.append(rem)
    initial_schemes[t0] = (pieces, scrap)
    losses[t0] = scrap
# 输出初始方案并尝试调整前一段
print("异常 时间 | 初始方案 (米)           | 调整方案 (米)             | 总切割损失 (米)")
print("----------|-------------------------|--------------------------|----------------")
prev_time = None
for (t0, L) in segments:
    init_pcs, init_scr = initial_schemes[t0]
    init_pcs_m = [round(l/10.0,1) for l in init_pcs]
    adjust_pcs_m = "-"
    total_scr = init_scr
    # 若上一次已有段且发生异常，尝试附加0.8m报废段调整
    if prev_time is not None:
        # 仅对上一次段做调整
        prev_pcs, prev_scr = initial_schemes[prev_time]
        new_pcs = prev_pcs.copy()
        # 附加0.8米到最小或最大段
        if prev_scr > 0:
            target = min(new_pcs)
        else:
            target = max(new_pcs)
        new_pcs.remove(target)
        new_piece = target + 8  # 分米
        new_pcs.append(new_piece)
        new_scr = sum(scrap_length(l/10.0)*10 for l in new_pcs)
        if new_scr < prev_scr:
            # 采用调整方案
            adjust_pcs_m = [round(l/10.0,1) for l in sorted(new_pcs)]
            # 更新上一次的总损失
            total_scr += new_scr - prev_scr
    # 输出本次结果
    print(f"{t0:6.1f}    | {init_pcs_m!s:<23} | {adjust_pcs_m!s:<24} | {total_scr/10:.1f}")
    prev_time = t0
# 问题3：目标8.5m (范围8.0~9.0) 和目标11.1m (范围10.6~11.6) 的切割方案
targets = [
    {"name": "目标8.5m (8.0~9.0)", "min": 80, "max": 90},
    {"name": "目标11.1m (10.6~11.6)", "min": 106, "max": 116}
]
segments = [(0.0, 44.8), (45.6, 52.2), (98.6, 32.1), (131.5, 58.5),
            (190.8, 41.7), (233.3, 31.9), (270.7, 56.4)]
for target in targets:
    tmin, tmax = target["min"], target["max"]
    print(f"\n===== {target['name']} =====")
    prev_pcs, prev_scr = None, None
    for t0, L in segments:
        L_dm = int(round(L*10))
        # 计算初始方案
        DP = [10**9]*(L_dm+1); DP[0]=0
        parent = [None]*(L_dm+1)
        for x in range(L_dm+1):
            if DP[x] >= 10**9: continue
            for l in range(48, 127):
                nxt = x + l
                if nxt > L_dm: break
                ld = l
                # 报废计算：结合基本要求8.0m(80dm)和新目标区间
                if ld < 80:
                    sc = l
                else:
                    # 在基础上只计算超出目标区的报废
                    sc = 0
                    if ld < tmin:
                        sc = tmin - ld
                    elif ld > tmax:
                        sc = ld - tmax
                if DP[x] + sc < DP[nxt]:
                    DP[nxt] = DP[x] + sc
                    parent[nxt] = (x, l)
        # 查最佳
        best = (10**9, None, None)
        for x in range(L_dm+1):
            if DP[x] >= 10**9: continue
            rem = L_dm - x
            if rem > 0:
                if rem < 48:
                    total = DP[x] + rem
                else:
                    if rem < 80:
                        total = DP[x] + rem
                    else:
                        sc = 0
                        if rem < tmin:
                            sc = tmin - rem
                        elif rem > tmax:
                            sc = rem - tmax
                        total = DP[x] + sc
            else:
                total = DP[x]
            if total < best[0]:
                best = (total, x, rem)
        scrap = best[0]; end_x = best[1]; rem = best[2]
        # 回溯方案
        pcs = []
        cur = end_x
        while cur:
            prev, l = parent[cur]
            pcs.append(l)
            cur = prev
        pcs.reverse()
        if rem >= 48: pcs.append(rem)
        init_scr = scrap
        init_pcs = pcs.copy()
        # 输出初始方案
        print(f"时间 {t0:.1f}min: 初始方案 {[round(p/10,1) for p in init_pcs]}, 损失 {init_scr/10:.1f}m", end="")
        # 尝试调整前一段
        if prev_pcs is not None:
            # 将0.8m报废附加到上一方案
            adj = prev_pcs.copy()
            if prev_scr > 0:
                tgt = min(adj)
            else:
                tgt = max(adj)
            adj.remove(tgt)
            new_seg = tgt + 8
            adj.append(new_seg)
            # 重新计算调整后损失
            new_scr = 0
            for l in adj:
                if l < 80:
                    new_scr += l
                else:
                    if l < tmin:
                        new_scr += (tmin - l)
                    elif l > tmax:
                        new_scr += (l - tmax)
            if new_scr < prev_scr:
                print(f"; 调整方案 {[round(p/10,1) for p in sorted(adj)]}, 损失 {new_scr/10:.1f}m")
            else:
                print("; 不需调整")
        else:
            print()
        prev_pcs = init_pcs
        prev_scr = init_scr
