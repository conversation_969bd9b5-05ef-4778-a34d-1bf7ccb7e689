# 问题2：模拟异常时刻切割方案（目标9.5米，范围9.0~10.0米）
anomaly_times = [0.0, 45.6, 98.6, 131.5, 190.8, 233.3, 266.0, 270.7, 327.9]
# 计算每段钢坯长度（异常之间的时间差减去0.8）
segments = []
for i in range(len(anomaly_times)-1):
    dt = anomaly_times[i+1] - anomaly_times[i]
    L = round(dt - 0.8, 1)
    if L >= 4.8:
        segments.append((anomaly_times[i], L))
# 对每段计算最优切割方案
initial_schemes = {}
losses = {}
for (t0, L) in segments:
    L_dm = int(round(L*10))
    # 使用前述DP函数计算最佳方案
    DP = [10**9]*(L_dm+1); DP[0] = 0
    parent = [None]*(L_dm+1)
    allowed = list(range(48, 127))
    for x in range(L_dm+1):
        if DP[x] >= 10**9: continue
        for l in allowed:
            nxt = x + l
            if nxt > L_dm: break
            ld = l/10.0
            # 计算分段报废长度（与问题1相同）
            if ld < 8.0: sc = l
            elif ld < 9.0: sc = int(round((9.0-ld)*10))
            elif ld <= 10.0: sc = 0
            else: sc = int(round((ld-10.0)*10))
            if DP[x] + sc < DP[nxt]:
                DP[nxt] = DP[x] + sc
                parent[nxt] = (x, l)
    # 找最小损失组合
    best = (10**9, None, None)
    for x in range(L_dm+1):
        if DP[x] >= 10**9: continue
        rem = L_dm - x
        if rem > 0:
            if rem < 48:
                total_sc = DP[x] + rem
            else:
                ld = rem/10.0
                if ld < 8.0: total_sc = DP[x] + rem
                elif ld < 9.0: total_sc = DP[x] + int(round((9.0-ld)*10))
                elif ld <= 10.0: total_sc = DP[x]
                else: total_sc = DP[x] + int(round((ld-10.0)*10))
        else:
            total_sc = DP[x]
        if total_sc < best[0]:
            best = (total_sc, x, rem)
    scrap = best[0]
    end_x = best[1]
    rem = best[2]
    # 回溯切割方案
    pieces = []
    cur = end_x
    while cur != 0:
        prev, l = parent[cur]
        pieces.append(l)
        cur = prev
    pieces.reverse()
    if rem and rem >= 48: 
        pieces.append(rem)
    initial_schemes[t0] = (pieces, scrap)
    losses[t0] = scrap
# 输出初始方案并尝试调整前一段
print("异常 时间 | 初始方案 (米)           | 调整方案 (米)             | 总切割损失 (米)")
print("----------|-------------------------|--------------------------|----------------")
prev_time = None
for (t0, L) in segments:
    init_pcs, init_scr = initial_schemes[t0]
    init_pcs_m = [round(l/10.0,1) for l in init_pcs]
    adjust_pcs_m = "-"
    total_scr = init_scr
    # 若上一次已有段且发生异常，尝试附加0.8m报废段调整
    if prev_time is not None:
        # 仅对上一次段做调整
        prev_pcs, prev_scr = initial_schemes[prev_time]
        new_pcs = prev_pcs.copy()
        # 附加0.8米到最小或最大段
        if prev_scr > 0:
            target = min(new_pcs)
        else:
            target = max(new_pcs)
        new_pcs.remove(target)
        new_piece = target + 8  # 分米
        new_pcs.append(new_piece)
        new_scr = sum(scrap_length(l/10.0)*10 for l in new_pcs)
        if new_scr < prev_scr:
            # 采用调整方案
            adjust_pcs_m = [round(l/10.0,1) for l in sorted(new_pcs)]
            # 更新上一次的总损失
            total_scr += new_scr - prev_scr
    # 输出本次结果
    print(f"{t0:6.1f}    | {init_pcs_m!s:<23} | {adjust_pcs_m!s:<24} | {total_scr/10:.1f}")
    prev_time = t0
